<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chatbot Maximize Functionality</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/root-style.css">
    <link rel="stylesheet" href="assets/css/chatbot/qkb-chatbot.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            min-height: 100vh;
        }
        .test-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .test-instructions ol {
            margin: 10px 0;
        }
        .test-instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Chatbot Maximize Functionality Test</h1>
        
        <div class="test-instructions">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Click the "Ask Q" button in the bottom right to open the chatbot</li>
                <li>Look for the maximize button (expand icon) in the chatbot controls</li>
                <li>Click the maximize button to expand the chatbot to full screen</li>
                <li>Verify the chatbot takes up most of the screen with proper margins</li>
                <li>Click the restore button (compress icon) to return to normal size</li>
                <li>Test on different screen sizes (resize your browser window)</li>
                <li>Verify the minimize button still works in both states</li>
            </ol>
        </div>

        <p>This test page simulates the chatbot maximize functionality. The chatbot should:</p>
        <ul>
            <li>Have a maximize button next to the minimize button</li>
            <li>Expand to take up most of the screen when maximized</li>
            <li>Show a restore icon when maximized</li>
            <li>Return to normal size when restored</li>
            <li>Maintain proper responsive behavior</li>
        </ul>

        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
    </div>

    <!-- Chatbot Button -->
    <div class="qkb-chat-button" role="button" aria-label="Open Chat">
        <div class="qkb-chat-branding">
            <div class="qkb-bot-avatar">
                <img src="assets/images/q.svg" alt="Ask Q" class="qkb-avatar qkb-default-avatar">
            </div>
            <span class="qkb-chat-button-text">Ask Q</span>
        </div>
    </div>

    <!-- Chatbot Container -->
    <div class="qkb-chatbot-container">
        <div class="qkb-chatbot-header">
            <div class="qkb-chatbot-branding">
                <div class="qkb-bot-avatar">
                    <img src="assets/images/q.svg" alt="Ask Q" class="qkb-avatar qkb-default-avatar">
                </div>
                <div class="qkb-bot-info">
                    <div class="qkb-bot-name">Ask Q</div>
                    <div class="qkb-assistant-name">Test Assistant</div>
                </div>
            </div>
            <div class="qkb-chatbot-controls">
                <button class="qkb-chatbot-control-button qkb-new-chat-button" title="New Chat">
                    New Chat
                </button>
                <button class="qkb-chatbot-control-button qkb-maximize-button" title="Maximize">
                    <svg class="maximize-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="15 3 21 3 21 9" />
                        <polyline points="9 21 3 21 3 15" />
                        <line x1="21" y1="3" x2="14" y2="10" />
                        <line x1="3" y1="21" x2="10" y2="14" />
                    </svg>
                    <svg class="restore-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="4 14 10 14 10 20" />
                        <polyline points="20 10 14 10 14 4" />
                        <line x1="14" y1="10" x2="21" y2="3" />
                        <line x1="3" y1="21" x2="10" y2="14" />
                    </svg>
                </button>
                <button class="qkb-chatbot-control-button qkb-minimize-button" title="Minimize">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9" />
                    </svg>
                </button>
            </div>
        </div>
        <div class="qkb-chatbot-messages">
            <div class="qkb-message qkb-bot-message">
                <div class="qkb-message-content">
                    <p>Hello! I'm your AI assistant. Click the maximize button to expand me to full screen!</p>
                </div>
            </div>
        </div>
        <div class="qkb-input-container">
            <div class="qkb-input-area">
                <textarea class="qkb-input-textarea" placeholder="How can I help you today?..." maxlength="500"></textarea>
            </div>
            <div class="qkb-input-buttons">
                <div class="qkb-submit-buttons">
                    <button class="qkb-send-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Simple test implementation
            const $container = $('.qkb-chatbot-container');
            const $button = $('.qkb-chat-button');
            
            // Open chat
            $button.on('click', function() {
                $container.addClass('visible');
                $button.fadeOut(300);
            });
            
            // Minimize chat
            $('.qkb-minimize-button').on('click', function() {
                $container.removeClass('visible maximized');
                $('body').removeClass('qkb-maximized-active');
                setTimeout(() => {
                    $button.fadeIn(300);
                }, 300);
            });
            
            // Toggle maximize
            $('.qkb-maximize-button').on('click', function() {
                const isMaximized = $container.hasClass('maximized');
                
                if (isMaximized) {
                    $container.removeClass('maximized');
                    $('body').removeClass('qkb-maximized-active');
                    $(this).attr('title', 'Maximize');
                } else {
                    $container.addClass('maximized');
                    $('body').addClass('qkb-maximized-active');
                    $(this).attr('title', 'Restore');
                }
            });
        });
    </script>
</body>
</html>
